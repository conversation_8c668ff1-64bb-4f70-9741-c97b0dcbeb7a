'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import Breadcrumb from '@/components/ui/Breadcrumb';
import Link from 'next/link';
import ManualAttendanceModal from '@/components/attendance/ManualAttendanceModal';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';


// Define attendance record interface
interface AttendanceRecord {
  attendance_id: string;
  check_in_time: string | null;
  check_out_time: string | null;
  created_at: string;
  created_by: string | null;
  date: string;
  employee_id: string;
  employee: Employee; // New: employee information is now included directly
  expected_end_time: string | null;
  expected_start_time: string | null;
  is_overtime: boolean | null;
  notes: string | null;
  overtime_hours: number | null;
  shift_id: string | null;
  source: string;
  source_record_id: string | null;
  status: string;
  total_hours: number | null;
  updated_at: string;
}

// Define employee interface
interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  employee_type_id: string | null;
  created_at: string;
  updated_at: string;
  id_number: string | null;
}

// Define department interface
interface Department {
  department_id: string;
  name: string;
  description: string;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
}

const AttendanceContent: React.FC = () => {
  const { companies } = useAuth();
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]); // Keep for filter dropdown
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Filter states
  const [selectedEmployee, setSelectedEmployee] = useState<string>('');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  // Set default date range to last 30 days
  const [startDate, setStartDate] = useState<Date | null>(() => {
    const date = new Date();
    date.setDate(date.getDate() - 30);
    return date;
  });
  const [endDate, setEndDate] = useState<Date | null>(new Date());
  const [singleDate, setSingleDate] = useState<Date | null>(new Date());
  const [filterType, setFilterType] = useState<'date-range' | 'employee' | 'single-date' | 'department'>('date-range');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(20); // Items per page (will be updated from API response)

  // Modal states
  const [isManualEntryModalOpen, setIsManualEntryModalOpen] = useState(false);
  const [isPDFDownloadModalOpen, setIsPDFDownloadModalOpen] = useState(false);

  // PDF Download states
  const [pdfDownloadType, setPdfDownloadType] = useState<'all' | 'employee'>('all');
  const [pdfSelectedEmployee, setPdfSelectedEmployee] = useState<string>('');
  const [pdfStartDate, setPdfStartDate] = useState<Date | null>(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
  const [pdfEndDate, setPdfEndDate] = useState<Date | null>(new Date());

  // Fetch employees and departments on component mount
  useEffect(() => {
    fetchEmployees();
    fetchDepartments();
  }, [companies]);

  // Create portal div for DatePicker on mount
  useEffect(() => {
    if (typeof document !== 'undefined') {
      let portalDiv = document.getElementById('date-picker-portal');
      if (!portalDiv) {
        portalDiv = document.createElement('div');
        portalDiv.id = 'date-picker-portal';
        portalDiv.style.zIndex = '9999';
        document.body.appendChild(portalDiv);
      }
    }
  }, []);

  // Fetch attendance records when filter or page changes
  useEffect(() => {
    if (companies && companies.length > 0) {
      fetchAttendanceRecords();
    }
  }, [companies, selectedEmployee, selectedDepartment, startDate, endDate, singleDate, filterType, currentPage]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedEmployee, selectedDepartment, startDate, endDate, singleDate, filterType]);

  // Function to fetch employees
  const fetchEmployees = async () => {
    try {
      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<{extend: {employees: Employee[]}, msg: string}>(
        `api/employees?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.employees) {
        setEmployees(response.extend.employees);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch employees');
    }
  };

  // Function to fetch departments
  const fetchDepartments = async () => {
    try {
      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<{
        departments: Department[];
        success: boolean;
      }>(
        `api/departments?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.departments) {
        setDepartments(response.departments);
      }
    } catch (error: any) {
      console.warn('Failed to fetch departments:', error.message);
      // Don't set error state for departments as it's not critical
    }
  };

  // Function to fetch attendance records
  const fetchAttendanceRecords = async () => {
    try {
      setIsLoading(true);
      setError('');

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      let endpoint = `api/attendance?company_id=${companyId}&page=${currentPage}&per_page=${itemsPerPage}`;

      // Add filter parameters based on selected filter type
      if (filterType === 'date-range' && startDate && endDate) {
        const formattedStartDate = startDate.toISOString().split('T')[0];
        const formattedEndDate = endDate.toISOString().split('T')[0];
        endpoint += `&start_date=${formattedStartDate}&end_date=${formattedEndDate}`;
      } else if (filterType === 'employee' && selectedEmployee) {
        endpoint += `&employee_id=${selectedEmployee}`;
      } else if (filterType === 'single-date' && singleDate) {
        const formattedDate = singleDate.toISOString().split('T')[0];
        endpoint += `&date=${formattedDate}`;
      } else if (filterType === 'department' && selectedDepartment) {
        endpoint += `&department_id=${selectedDepartment}`;
      }

      // Always apply department filter if selected (independent of filter type)
      if (selectedDepartment && filterType !== 'department') {
        endpoint += `&department_id=${selectedDepartment}`;
      }



      const response = await apiGet<{
        attendance_records: AttendanceRecord[],
        status: string,
        pagination?: {
          page: number;
          total_pages: number;
          per_page: number;
          total_records: number;
          count: number;
          has_next: boolean;
          has_prev: boolean;
          next: number | null;
          previous: number | null;
        }
      }>(
        endpoint,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.attendance_records) {
        setAttendanceRecords(response.attendance_records);

        // Update pagination info if available
        if (response.pagination) {
          setTotalPages(Math.max(1, response.pagination.total_pages || 1));
          setTotalRecords(Math.max(0, response.pagination.total_records || 0));
          setItemsPerPage(response.pagination.per_page || 20);
        } else {
          // If no pagination info, but we have records, estimate pagination
          const recordCount = response.attendance_records.length;
          if (recordCount === itemsPerPage) {
            // Likely there are more pages
            setTotalPages(2); // Show pagination to allow navigation
            setTotalRecords(recordCount);
          } else {
            setTotalPages(1);
            setTotalRecords(recordCount);
          }
        }
      } else {
        setAttendanceRecords([]);
        setTotalPages(1);
        setTotalRecords(0);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch attendance records');
      setAttendanceRecords([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Format time for display
  const formatTime = (timeString: string | null) => {
    if (!timeString) return 'N/A';

    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      return timeString;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (error) {
      return dateString;
    }
  };

  // Format hours for display
  const formatHours = (hours: number | null) => {
    if (hours === null) return 'N/A';

    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);

    return `${wholeHours}h ${minutes}m`;
  };

  // Get employee name from attendance record (now included directly)
  const getEmployeeName = (record: AttendanceRecord) => {
    return record.employee?.full_name || 'Unknown Employee';
  };





  // Open manual entry modal
  const openManualEntryModal = () => {
    setIsManualEntryModalOpen(true);
  };

  // Close manual entry modal
  const closeManualEntryModal = () => {
    setIsManualEntryModalOpen(false);
  };

  // Handle successful manual entry
  const handleManualEntrySuccess = () => {
    fetchAttendanceRecords();
  };

  // Handle PDF download with enhanced options
  const handlePDFDownload = async (
    downloadType: 'all' | 'employee' = 'all',
    employeeId?: string,
    startDate?: Date | null,
    endDate?: Date | null
  ) => {
    try {
      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      // Import the API utility to get the correct base URL
      const { createApiUrl } = await import('@/lib/api');

      // Build the download endpoint
      let endpoint = `api/attendance/download/pdf?company_id=${companyId}`;

      // Add date parameters
      if (startDate && endDate) {
        const formattedStartDate = startDate.toISOString().split('T')[0];
        const formattedEndDate = endDate.toISOString().split('T')[0];
        endpoint += `&start_date=${formattedStartDate}&end_date=${formattedEndDate}`;
      } else {
        // Default to current month if no date filter
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        endpoint += `&start_date=${startOfMonth.toISOString().split('T')[0]}&end_date=${endOfMonth.toISOString().split('T')[0]}`;
      }

      // Add employee filter if selected
      if (downloadType === 'employee' && employeeId) {
        endpoint += `&employee_id=${employeeId}`;
      }

      // Add department filter if currently applied
      if (selectedDepartment) {
        endpoint += `&department_id=${selectedDepartment}`;
      }

      // Create the full URL using the API utility
      const fullUrl = createApiUrl(endpoint);

      // Make the request with proper authorization
      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;

        // Generate filename based on download type
        const dateStr = new Date().toISOString().split('T')[0];
        const employeeName = downloadType === 'employee' && employeeId
          ? employees.find(emp => emp.employee_id === employeeId)?.full_name || 'employee'
          : 'all-employees';
        a.download = `attendance-report-${employeeName}-${dateStr}.pdf`;

        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        throw new Error('Failed to download PDF');
      }
    } catch (error: any) {
      console.error('Error downloading PDF:', error);
      setError(error.message || 'Failed to download PDF report');
    }
  };

  // Open PDF download modal
  const openPDFDownloadModal = () => {
    setIsPDFDownloadModalOpen(true);
  };

  // Close PDF download modal
  const closePDFDownloadModal = () => {
    setIsPDFDownloadModalOpen(false);
  };

  // Handle PDF download from modal
  const handleModalPDFDownload = async () => {
    await handlePDFDownload(pdfDownloadType, pdfSelectedEmployee, pdfStartDate, pdfEndDate);
    closePDFDownloadModal();
  };

  return (
    <div className="space-y-6">


      {/* Manual Attendance Entry Modal */}
      <ManualAttendanceModal
        isOpen={isManualEntryModalOpen}
        onClose={closeManualEntryModal}
        onSuccess={handleManualEntrySuccess}
      />

      {/* PDF Download Modal */}
      {isPDFDownloadModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Download Attendance Report</h3>
              <button
                onClick={closePDFDownloadModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              {/* Download Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Download Type
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="downloadType"
                      value="all"
                      checked={pdfDownloadType === 'all'}
                      onChange={(e) => setPdfDownloadType(e.target.value as 'all' | 'employee')}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">All Employees</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="downloadType"
                      value="employee"
                      checked={pdfDownloadType === 'employee'}
                      onChange={(e) => setPdfDownloadType(e.target.value as 'all' | 'employee')}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Specific Employee</span>
                  </label>
                </div>
              </div>

              {/* Employee Selection (only show if employee type is selected) */}
              {pdfDownloadType === 'employee' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Select Employee
                  </label>
                  <select
                    value={pdfSelectedEmployee}
                    onChange={(e) => setPdfSelectedEmployee(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  >
                    <option value="">Choose an employee...</option>
                    {employees.map((employee) => (
                      <option key={employee.employee_id} value={employee.employee_id}>
                        {employee.full_name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Date Range Selection */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={pdfStartDate ? pdfStartDate.toISOString().split('T')[0] : ''}
                    onChange={(e) => setPdfStartDate(e.target.value ? new Date(e.target.value) : null)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={pdfEndDate ? pdfEndDate.toISOString().split('T')[0] : ''}
                    onChange={(e) => setPdfEndDate(e.target.value ? new Date(e.target.value) : null)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  onClick={closePDFDownloadModal}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  Cancel
                </button>
                <button
                  onClick={handleModalPDFDownload}
                  disabled={pdfDownloadType === 'employee' && !pdfSelectedEmployee}
                  className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Download PDF
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h1 className="text-2xl font-bold text-secondary-dark">Attendance Management</h1>
        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          <Link
            href="/dashboard/hr/attendance/daily"
            className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            title="Daily Report"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span className="hidden md:inline">Daily Report</span>
          </Link>
          <Link
            href="/dashboard/hr/attendance/dashboard"
            className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            title="Statistics"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span className="hidden md:inline">Statistics</span>
          </Link>
          <Link
            href="/dashboard/hr/attendance/employee"
            className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            title="Employee Stats"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span className="hidden md:inline">Employee Stats</span>
          </Link>
          <button
            onClick={openPDFDownloadModal}
            className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            title="Download PDF"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span className="hidden md:inline">Download PDF</span>
          </button>
          <button
            onClick={openManualEntryModal}
            className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-white bg-primary rounded-md shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            title="Manual Entry"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span className="hidden md:inline">Manual Entry</span>
          </button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <Breadcrumb
        items={[
          { label: 'Dashboard', href: '/dashboard/hr' },
          { label: 'Attendance', isActive: true }
        ]}
      />

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      <DashboardCard title="Attendance Filters">
        <div className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="col-span-1 sm:col-span-2 lg:col-span-1">
              <label className="block text-sm font-medium text-secondary-dark mb-1">
                Filter Type
              </label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="date-range">Date Range</option>
                <option value="employee">By Employee</option>
                <option value="single-date">Single Date</option>
                <option value="department">By Department</option>
              </select>
            </div>

            {filterType === 'date-range' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-secondary-dark mb-1">
                    Start Date
                  </label>
                  <DatePicker
                    selected={startDate}
                    onChange={(date) => setStartDate(date)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    dateFormat="yyyy-MM-dd"
                    popperClassName="!z-[9999]"
                    popperPlacement="bottom-start"
                    portalId="date-picker-portal"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-dark mb-1">
                    End Date
                  </label>
                  <DatePicker
                    selected={endDate}
                    onChange={(date) => setEndDate(date)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    dateFormat="yyyy-MM-dd"
                    minDate={startDate || undefined}
                    popperClassName="!z-[9999]"
                    popperPlacement="bottom-start"
                    portalId="date-picker-portal"
                  />
                </div>
              </>
            )}

            {filterType === 'employee' && (
              <div className="col-span-1 sm:col-span-2">
                <label className="block text-sm font-medium text-secondary-dark mb-1">
                  Select Employee
                </label>
                <select
                  value={selectedEmployee}
                  onChange={(e) => setSelectedEmployee(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">All Employees</option>
                  {employees.map((employee) => (
                    <option key={employee.employee_id} value={employee.employee_id}>
                      {employee.full_name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {filterType === 'single-date' && (
              <div className="col-span-1 sm:col-span-2">
                <label className="block text-sm font-medium text-secondary-dark mb-1">
                  Select Date
                </label>
                <DatePicker
                  selected={singleDate}
                  onChange={(date) => setSingleDate(date)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  dateFormat="yyyy-MM-dd"
                  popperClassName="!z-[9999]"
                  popperPlacement="bottom-start"
                  portalId="date-picker-portal"
                />
              </div>
            )}

            {filterType === 'department' && (
              <div className="col-span-1 sm:col-span-2">
                <label className="block text-sm font-medium text-secondary-dark mb-1">
                  Select Department
                </label>
                <select
                  value={selectedDepartment}
                  onChange={(e) => setSelectedDepartment(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">All Departments</option>
                  {departments.map((department) => (
                    <option key={department.department_id} value={department.department_id}>
                      {department.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>
        </div>
      </DashboardCard>

      <DashboardCard title="Attendance Records">
        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading attendance records...</p>
          </div>
        ) : attendanceRecords.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-secondary">No attendance records found for the selected filters.</p>
          </div>
        ) : (
          <div className="relative flex flex-col h-[70vh] md:h-[60vh]">
            {/* Scrollable Table Container */}
            <div className="overflow-y-auto overflow-x-auto flex-1">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0 z-[1]">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Check In
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Check Out
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Hours
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Source
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {attendanceRecords.map((record) => (
                    <tr key={record.attendance_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-secondary-dark">
                          {getEmployeeName(record)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{formatDate(record.date)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{formatTime(record.check_in_time)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{formatTime(record.check_out_time)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{formatHours(record.total_hours)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          record.status === 'present'
                            ? 'bg-green-100 text-green-800'
                            : record.status === 'absent'
                            ? 'bg-red-100 text-red-800'
                            : record.status === 'late'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{record.source}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex flex-col md:flex-row md:justify-end space-y-2 md:space-y-0 md:space-x-3">
                          {/* <button
                            className="text-primary hover:text-primary-dark"
                            onClick={() => openDetailsModal(record.attendance_id, record.employee_id)}
                          >
                            View
                          </button> */}
                          <Link
                            href={`/dashboard/hr/attendance/employee/${record.employee_id}`}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            Stats
                          </Link>
                          {/* <button
                            className="text-secondary-dark hover:text-secondary"
                          >
                            Edit
                          </button> */}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination Controls */}
            {(totalPages > 1 || attendanceRecords.length === itemsPerPage) && (
              <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing{' '}
                      <span className="font-medium">{Math.max(1, (currentPage - 1) * itemsPerPage + 1)}</span>
                      {' '}to{' '}
                      <span className="font-medium">
                        {Math.min(currentPage * itemsPerPage, totalRecords || 0)}
                      </span>
                      {' '}of{' '}
                      <span className="font-medium">{totalRecords || 0}</span>
                      {' '}results
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">Previous</span>
                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </button>

                      {/* Page numbers */}
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <button
                            key={pageNum}
                            onClick={() => setCurrentPage(pageNum)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              currentPage === pageNum
                                ? 'z-10 bg-primary border-primary text-white'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}

                      <button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">Next</span>
                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </DashboardCard>
    </div>
  );
};

export default AttendanceContent;
